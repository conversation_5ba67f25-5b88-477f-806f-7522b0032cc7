import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/custom_widgets/otp_screen.dart';
import 'package:rideoon/views/authentication/userauth/sign_in.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/providers/toast_provider.dart';

/// Account verification screen for newly registered users
/// 
/// This screen is shown after successful sign-up to verify the user's account
/// using the OTP code sent to their email.
class UserAccountVerification extends StatefulWidget {
  /// The user's email address
  final String email;
  
  /// The user's phone number
  final String phoneNumber;
  
  /// The user's first name for personalization
  final String firstName;

  const UserAccountVerification({
    super.key,
    required this.email,
    required this.phoneNumber,
    required this.firstName,
  });

  @override
  State<UserAccountVerification> createState() => _UserAccountVerificationState();
}

class _UserAccountVerificationState extends State<UserAccountVerification> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON><PERSON>(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            children: [
              // Header section
              _buildHeader(),
              
              const SizedBox(height: 32),
              
              // OTP verification section
              Expanded(
                child: OTPVerificationScreen(
                  verificationMethod: 'email',
                  contactInfo: _maskEmail(widget.email),
                  onVerificationSuccess: _handleVerificationSuccess,
                  onResendOTP: _handleResendOTP,
                  onBack: _handleBack,
                  isLoading: _isLoading,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Back button
        Row(
          children: [
            IconButton(
              onPressed: _handleBack,
              icon: const Icon(
                Icons.arrow_back,
                color: AppColors.black,
              ),
            ),
            const Spacer(),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Title and description
        Text(
          'Verify Your Account',
          style: TextStyle(
            fontSize: 28,
            fontWeight: FontWeight.w700,
            color: AppColors.black,
          ),
        ),
        
        const SizedBox(height: 8),
        
        Text(
          'Hi ${widget.firstName}! We\'ve sent a verification code to your email address. Please enter it below to activate your account.',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w400,
            color: AppColors.black.withValues(alpha: 0.7),
            height: 1.5,
          ),
        ),
      ],
    );
  }

  String _maskEmail(String email) {
    final parts = email.split('@');
    if (parts.length != 2) return email;
    
    final username = parts[0];
    final domain = parts[1];
    
    if (username.length <= 2) return email;
    
    final maskedUsername = username.substring(0, 2) + 
                          '*' * (username.length - 2);
    
    return '$maskedUsername@$domain';
  }

  Future<void> _handleVerificationSuccess(String otp) async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Call the verification API
      final success = await AuthService.verifyNewUserAccount(
        id: widget.email,
        otp: otp,
        userType: 'Client',
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (success) {
          // Show success message
          Toast.success('Account verified successfully! You can now sign in.');
          
          // Navigate to sign-in screen
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(
              builder: (context) => const UserSignIn(),
            ),
            (route) => false, // Remove all previous routes
          );
        } else {
          Toast.error('Invalid verification code. Please try again.');
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        Toast.error('Verification failed. Please try again.');
      }
    }
  }

  Future<void> _handleResendOTP() async {
    try {
      // Call resend OTP API (if available)
      // For now, just show a message
      Toast.info('A new verification code has been sent to your email.');
    } catch (e) {
      Toast.error('Failed to resend verification code. Please try again.');
    }
  }

  void _handleBack() {
    Navigator.of(context).pop();
  }
}
