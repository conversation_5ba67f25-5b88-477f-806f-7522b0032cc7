import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for managing saved addresses
class AddressService {
  static const String _savedAddressesKey = 'saved_addresses';

  /// Save an address to the saved addresses list
  static Future<void> saveAddress(Map<String, dynamic> addressData) async {
    final prefs = await SharedPreferences.getInstance();
    final addressesJson = prefs.getString(_savedAddressesKey);
    List<Map<String, dynamic>> addresses = [];

    if (addressesJson != null) {
      final addressesList = jsonDecode(addressesJson) as List;
      addresses = addressesList.cast<Map<String, dynamic>>();
    }

    // Check if address already exists (by full address)
    final existingIndex = addresses.indexWhere((addr) => 
      addr['fullAddress']?.toString().toLowerCase() == 
      addressData['fullAddress']?.toString().toLowerCase());

    if (existingIndex != -1) {
      // Update existing address
      addresses[existingIndex] = {
        ...addressData,
        'id': addresses[existingIndex]['id'],
        'lastUsed': DateTime.now().millisecondsSinceEpoch,
      };
    } else {
      // Add new address
      addresses.insert(0, {
        ...addressData,
        'id': DateTime.now().millisecondsSinceEpoch.toString(),
        'lastUsed': DateTime.now().millisecondsSinceEpoch,
      });
    }

    // Keep only the last 20 addresses
    if (addresses.length > 20) {
      addresses = addresses.take(20).toList();
    }

    await prefs.setString(_savedAddressesKey, jsonEncode(addresses));
  }

  /// Get all saved addresses
  static Future<List<Map<String, dynamic>>> getSavedAddresses() async {
    final prefs = await SharedPreferences.getInstance();
    final addressesJson = prefs.getString(_savedAddressesKey);
    
    if (addressesJson != null) {
      final addressesList = jsonDecode(addressesJson) as List;
      final addresses = addressesList.cast<Map<String, dynamic>>();
      
      // Sort by last used (most recent first)
      addresses.sort((a, b) => 
        (b['lastUsed'] ?? 0).compareTo(a['lastUsed'] ?? 0));
      
      return addresses;
    }
    return [];
  }

  /// Delete a saved address
  static Future<void> deleteAddress(String addressId) async {
    final prefs = await SharedPreferences.getInstance();
    final addressesJson = prefs.getString(_savedAddressesKey);
    
    if (addressesJson != null) {
      final addressesList = jsonDecode(addressesJson) as List;
      final addresses = addressesList.cast<Map<String, dynamic>>();
      
      addresses.removeWhere((addr) => addr['id'] == addressId);
      
      await prefs.setString(_savedAddressesKey, jsonEncode(addresses));
    }
  }

  /// Update an existing address
  static Future<void> updateAddress(String addressId, Map<String, dynamic> updatedData) async {
    final prefs = await SharedPreferences.getInstance();
    final addressesJson = prefs.getString(_savedAddressesKey);
    
    if (addressesJson != null) {
      final addressesList = jsonDecode(addressesJson) as List;
      final addresses = addressesList.cast<Map<String, dynamic>>();
      
      final index = addresses.indexWhere((addr) => addr['id'] == addressId);
      if (index != -1) {
        addresses[index] = {
          ...addresses[index],
          ...updatedData,
          'lastUsed': DateTime.now().millisecondsSinceEpoch,
        };
        
        await prefs.setString(_savedAddressesKey, jsonEncode(addresses));
      }
    }
  }

  /// Clear all saved addresses
  static Future<void> clearAllAddresses() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_savedAddressesKey);
  }

  /// Get addresses by type (pickup or receiver)
  static Future<List<Map<String, dynamic>>> getAddressesByType(String type) async {
    final addresses = await getSavedAddresses();
    return addresses.where((addr) => addr['type'] == type).toList();
  }

  /// Mark address as recently used
  static Future<void> markAsRecentlyUsed(String addressId) async {
    final prefs = await SharedPreferences.getInstance();
    final addressesJson = prefs.getString(_savedAddressesKey);
    
    if (addressesJson != null) {
      final addressesList = jsonDecode(addressesJson) as List;
      final addresses = addressesList.cast<Map<String, dynamic>>();
      
      final index = addresses.indexWhere((addr) => addr['id'] == addressId);
      if (index != -1) {
        addresses[index]['lastUsed'] = DateTime.now().millisecondsSinceEpoch;
        await prefs.setString(_savedAddressesKey, jsonEncode(addresses));
      }
    }
  }
}
