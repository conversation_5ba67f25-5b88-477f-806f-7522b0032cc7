import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';

/// A reusable OTP verification screen widget
///
/// This widget provides a complete OTP verification interface with
/// responsive design and consistent styling throughout the app.
///
/// Features:
/// - Responsive design for mobile, tablet, and smartwatch
/// - 4-digit OTP input with automatic focus management
/// - Customizable verification method (email/SMS)
/// - Resend OTP functionality with countdown timer
/// - Form validation and error handling
/// - Loading states and success feedback
/// - Consistent styling with app theme
/// - Accessibility support
class OTPVerificationScreen extends StatefulWidget {
  /// The verification method used (email or SMS)
  final String verificationMethod;

  /// The masked contact information (email or phone)
  final String contactInfo;

  /// Callback function when OTP verification is successful
  final Function(String otp)? onVerificationSuccess;

  /// Callback function when resend OTP is requested
  final VoidCallback? onResendOTP;

  /// Callback function when back button is pressed
  final VoidCallback? onBack;

  /// Whether to show loading state
  final bool isLoading;

  const OTPVerificationScreen({
    super.key,
    required this.verificationMethod,
    required this.contactInfo,
    this.onVerificationSuccess,
    this.onResendOTP,
    this.onBack,
    this.isLoading = false,
  });

  @override
  State<OTPVerificationScreen> createState() => _OTPVerificationScreenState();
}

class _OTPVerificationScreenState extends State<OTPVerificationScreen> {
  final List<TextEditingController> _controllers = List.generate(4, (index) => TextEditingController());
  final List<FocusNode> _focusNodes = List.generate(4, (index) => FocusNode());
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  int _resendCountdown = 0;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _isLoading = widget.isLoading;
    _startResendCountdown();
  }

  @override
  void dispose() {
    for (var controller in _controllers) {
      controller.dispose();
    }
    for (var focusNode in _focusNodes) {
      focusNode.dispose();
    }
    super.dispose();
  }

  void _startResendCountdown() {
    setState(() {
      _resendCountdown = 60; // 60 seconds countdown
    });

    Future.doWhile(() async {
      await Future.delayed(const Duration(seconds: 1));
      if (mounted) {
        setState(() {
          _resendCountdown--;
        });
        return _resendCountdown > 0;
      }
      return false;
    });
  }

  void _onDigitChanged(String value, int index) {
    setState(() {
      _errorMessage = '';
    });

    if (value.isNotEmpty) {
      // Move to next field
      if (index < 3) {
        _focusNodes[index + 1].requestFocus();
      } else {
        // Last digit entered, attempt verification
        _focusNodes[index].unfocus();
        _verifyOTP();
      }
    }
  }

  void _onDigitBackspace(int index) {
    if (_controllers[index].text.isEmpty && index > 0) {
      _focusNodes[index - 1].requestFocus();
    }
  }

  String _getOTPCode() {
    return _controllers.map((controller) => controller.text).join();
  }

  bool _isOTPComplete() {
    return _getOTPCode().length == 4;
  }

  void _clearOTP() {
    for (var controller in _controllers) {
      controller.clear();
    }
    _focusNodes[0].requestFocus();
    setState(() {
      _errorMessage = '';
    });
  }

  Future<void> _verifyOTP() async {
    if (!_isOTPComplete()) {
      setState(() {
        _errorMessage = 'Please enter the complete 4-digit code';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = '';
    });

    final otpCode = _getOTPCode();

    // Call the verification callback if provided
    if (widget.onVerificationSuccess != null) {
      widget.onVerificationSuccess!(otpCode);
    } else {
      // Fallback: simulate validation for demo purposes
      try {
        await Future.delayed(const Duration(seconds: 2));

        // Demo validation (only used when no callback is provided)
        if (otpCode == '1234') {
          // Success - but no callback to handle it
          setState(() {
            _isLoading = false;
          });
        } else {
          setState(() {
            _errorMessage = 'Invalid OTP code. Please try again.';
            _isLoading = false;
          });
          _clearOTP();
        }
      } catch (e) {
        setState(() {
          _errorMessage = 'Verification failed. Please try again.';
          _isLoading = false;
        });
        _clearOTP();
      }
    }
  }

  void _handleResendOTP() {
    if (_resendCountdown > 0) return;

    _clearOTP();
    _startResendCountdown();

    if (widget.onResendOTP != null) {
      widget.onResendOTP!();
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('OTP code sent to ${widget.contactInfo}'),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _navigateBack() {
    if (widget.onBack != null) {
      widget.onBack!();
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getHorizontalPadding(context),
                vertical: _getVerticalPadding(context),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: _getSpacing(context, 20)),

                  // Back button and title
                  _buildHeader(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Description
                  _buildDescription(context),

                  SizedBox(height: _getSpacing(context, 40)),

                  // OTP Input Fields
                  _buildOTPFields(context),

                  if (_errorMessage.isNotEmpty) ...[
                    SizedBox(height: _getSpacing(context, 16)),
                    _buildErrorMessage(context),
                  ],

                  SizedBox(height: _getSpacing(context, 32)),

                  // Resend OTP
                  _buildResendSection(context),

                  SizedBox(height: _getSpacing(context, 40)),

                  // Continue button
                  _buildContinueButton(context),

                  SizedBox(height: _getSpacing(context, 20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: _navigateBack,
          child: Container(
            width: _getIconSize(context) + 8,
            height: _getIconSize(context) + 8,
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context)),
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              size: _getIconSize(context),
              color: AppColors.black,
            ),
          ),
        ),
        SizedBox(width: _getSpacing(context, 16)),
        Text(
          'OTP verification',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getHeadingFontSize(context),
            fontFamily: 'Bricolage Grotesque',
            fontWeight: FontWeight.w600,
            letterSpacing: -0.8,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: 'Enter OTP code we sent to ',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.7),
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
              height: 1.5,
            ),
          ),
          TextSpan(
            text: widget.contactInfo,
            style: TextStyle(
              color: AppColors.primary,
              fontSize: _getBodyFontSize(context),
              fontFamily: 'Inter',
              fontWeight: FontWeight.w500,
              height: 1.5,
            ),
          ),
        ],
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildOTPFields(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: List.generate(4, (index) {
        return Container(
          width: _getOTPFieldSize(context),
          height: _getOTPFieldSize(context),
          decoration: BoxDecoration(
            border: Border.all(
              color: _controllers[index].text.isNotEmpty
                  ? AppColors.primary
                  : AppColors.black.withValues(alpha: 0.2),
              width: _controllers[index].text.isNotEmpty ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          ),
          child: Center(
            child: TextFormField(
              controller: _controllers[index],
              focusNode: _focusNodes[index],
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              maxLength: 1,
              style: TextStyle(
                fontSize: _getOTPFontSize(context),
                fontWeight: FontWeight.w600,
                color: AppColors.black,
              ),
              decoration: const InputDecoration(
                border: InputBorder.none,
                counterText: '',
              ),
              onChanged: (value) => _onDigitChanged(value, index),
              onTap: () {
                if (_controllers[index].text.isNotEmpty) {
                  _controllers[index].selection = TextSelection.fromPosition(
                    TextPosition(offset: _controllers[index].text.length),
                  );
                }
              },
            ),
          ),
        );
      }),
    );
  }

  Widget _buildErrorMessage(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(_getSpacing(context, 12)),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(_getBorderRadius(context)),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: AppColors.error,
            size: _getIconSize(context),
          ),
          SizedBox(width: _getSpacing(context, 8)),
          Expanded(
            child: Text(
              _errorMessage,
              style: TextStyle(
                color: AppColors.error,
                fontSize: _getSmallFontSize(context),
                fontFamily: 'Inter',
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResendSection(BuildContext context) {
    return Center(
      child: Column(
        children: [
          Text(
            'Didn\'t receive the code?',
            style: TextStyle(
              color: AppColors.black.withValues(alpha: 0.6),
              fontSize: _getSmallFontSize(context),
              fontFamily: 'Inter',
              fontWeight: FontWeight.w400,
            ),
          ),
          SizedBox(height: _getSpacing(context, 8)),
          GestureDetector(
            onTap: _resendCountdown > 0 ? null : _handleResendOTP,
            child: Text(
              _resendCountdown > 0
                  ? 'Resend in ${_resendCountdown}s'
                  : 'Resend OTP',
              style: TextStyle(
                color: _resendCountdown > 0
                    ? AppColors.black.withValues(alpha: 0.4)
                    : AppColors.primary,
                fontSize: _getBodyFontSize(context),
                fontFamily: 'Inter',
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContinueButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: _getButtonHeight(context),
      child: ElevatedButton(
        onPressed: (_isLoading || !_isOTPComplete()) ? null : _verifyOTP,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? SizedBox(
                width: _getSpacing(context, 20),
                height: _getSpacing(context, 20),
                child: const CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Continue',
                style: TextStyle(
                  fontSize: _getBodyFontSize(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getVerticalPadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 12; // Short screens
    if (screenHeight > 800) return 24; // Tall screens
    return 16; // Normal screens
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 26; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.85;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getSmallFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet
    } else {
      baseSize = 12; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getOTPFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 18; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 28; // Tablet
    } else {
      baseSize = 24; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 18; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 24; // Tablet
    } else {
      baseSize = 20; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 8; // Smartwatch
    if (screenWidth > 600) return 16; // Tablet
    return 12; // Mobile
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 56; // Tablet
    } else {
      baseHeight = 48; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }

  double _getOTPFieldSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 45; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 70; // Tablet
    } else {
      baseSize = 60; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }
}