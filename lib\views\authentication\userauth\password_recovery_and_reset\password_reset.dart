import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/authentication/userauth/sign_in.dart';

class UserPasswordReset extends StatefulWidget {
  const UserPasswordReset({super.key});

  @override
  State<UserPasswordReset> createState() => _UserPasswordResetState();
}

class _UserPasswordResetState extends State<UserPasswordReset> {
  final _formKey = GlobalKey<FormState>();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  bool _isNewPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  bool _isLoading = false;

  @override
  void dispose() {
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _navigateBack() {
    Navigator.of(context).pop();
  }

  void _toggleNewPasswordVisibility() {
    setState(() {
      _isNewPasswordVisible = !_isNewPasswordVisible;
    });
  }

  void _toggleConfirmPasswordVisibility() {
    setState(() {
      _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
    });
  }

  String? _validateNewPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please enter a new password';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (!RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)').hasMatch(value)) {
      return 'Password must contain uppercase, lowercase, and number';
    }
    return null;
  }

  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != _newPasswordController.text) {
      return 'Passwords do not match';
    }
    return null;
  }

  Future<void> _handleResetPassword() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    // Simulate API call to reset password
    await Future.delayed(const Duration(seconds: 2));

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      // Show success message
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Password reset successfully!'),
          backgroundColor: AppColors.success,
        ),
      );

      // Navigate back to sign in screen
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(
          builder: (context) => const UserSignIn(),
        ),
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getHorizontalPadding(context),
                vertical: _getVerticalPadding(context),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: _getSpacing(context, 20)),

                  // Back button and title
                  _buildHeader(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Description
                  _buildDescription(context),

                  SizedBox(height: _getSpacing(context, 40)),

                  // Password reset form
                  _buildPasswordResetForm(context),

                  SizedBox(height: _getSpacing(context, 40)),

                  // Reset password button
                  _buildResetPasswordButton(context),

                  SizedBox(height: _getSpacing(context, 20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Row(
      children: [
        GestureDetector(
          onTap: _navigateBack,
          child: Container(
            width: _getIconSize(context) + 8,
            height: _getIconSize(context) + 8,
            decoration: BoxDecoration(
              color: AppColors.black.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(_getBorderRadius(context)),
            ),
            child: Icon(
              Icons.arrow_back_ios_new,
              size: _getIconSize(context),
              color: AppColors.black,
            ),
          ),
        ),
        SizedBox(width: _getSpacing(context, 16)),
        Text(
          'Reset password',
          style: TextStyle(
            color: AppColors.black,
            fontSize: _getHeadingFontSize(context),
            fontFamily: 'Bricolage Grotesque',
            fontWeight: FontWeight.w600,
            letterSpacing: -0.8,
          ),
        ),
      ],
    );
  }

  Widget _buildDescription(BuildContext context) {
    return Text(
      'Create a new password for your account. Make sure it\'s strong and secure.',
      style: TextStyle(
        color: AppColors.black.withValues(alpha: 0.7),
        fontSize: _getBodyFontSize(context),
        fontFamily: 'Inter',
        fontWeight: FontWeight.w400,
        height: 1.5,
      ),
    );
  }

  Widget _buildPasswordResetForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // New Password Field
          _buildTextFormField(
            controller: _newPasswordController,
            labelText: 'New Password',
            hintText: 'Enter your new password',
            prefixIcon: Icons.lock_outline,
            obscureText: !_isNewPasswordVisible,
            suffixIcon: GestureDetector(
              onTap: _toggleNewPasswordVisibility,
              child: Icon(
                _isNewPasswordVisible ? Icons.visibility : Icons.visibility_off,
                color: AppColors.black.withValues(alpha: 0.6),
                size: _getIconSize(context),
              ),
            ),
            validator: _validateNewPassword,
          ),

          SizedBox(height: _getSpacing(context, 20)),

          // Confirm Password Field
          _buildTextFormField(
            controller: _confirmPasswordController,
            labelText: 'Confirm Password',
            hintText: 'Confirm your new password',
            prefixIcon: Icons.lock_outline,
            obscureText: !_isConfirmPasswordVisible,
            suffixIcon: GestureDetector(
              onTap: _toggleConfirmPasswordVisibility,
              child: Icon(
                _isConfirmPasswordVisible ? Icons.visibility : Icons.visibility_off,
                color: AppColors.black.withValues(alpha: 0.6),
                size: _getIconSize(context),
              ),
            ),
            validator: _validateConfirmPassword,
          ),
        ],
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      obscureText: obscureText,
      validator: validator,
      style: TextStyle(
        fontSize: _getBodyFontSize(context),
        color: AppColors.black,
      ),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: Icon(
          prefixIcon,
          color: AppColors.black.withValues(alpha: 0.6),
          size: _getIconSize(context),
        ),
        suffixIcon: suffixIcon,
        labelStyle: TextStyle(
          fontSize: _getBodyFontSize(context),
          color: AppColors.black.withValues(alpha: 0.6),
        ),
        hintStyle: TextStyle(
          fontSize: _getBodyFontSize(context),
          color: AppColors.black.withValues(alpha: 0.4),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: AppColors.error,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: AppColors.error,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 16),
        ),
      ),
    );
  }

  Widget _buildResetPasswordButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: _getButtonHeight(context),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleResetPassword,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? SizedBox(
                width: _getSpacing(context, 20),
                height: _getSpacing(context, 20),
                child: const CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Reset Password',
                style: TextStyle(
                  fontSize: _getBodyFontSize(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getVerticalPadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 12; // Short screens
    if (screenHeight > 800) return 24; // Tall screens
    return 16; // Normal screens
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 26; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.85;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 18; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 24; // Tablet
    } else {
      baseSize = 20; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 6; // Smartwatch
    if (screenWidth > 600) return 12; // Tablet
    return 8; // Mobile
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 56; // Tablet
    } else {
      baseHeight = 48; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }
}