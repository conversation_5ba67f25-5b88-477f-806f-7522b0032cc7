import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:rideoon/services/config_service.dart';
import 'package:rideoon/models/auth/sign_up_request.dart';
import 'package:rideoon/models/auth/sign_up_response.dart';
import 'package:rideoon/models/auth/sign_in_request.dart';
import 'package:rideoon/models/auth/sign_in_response.dart';
import 'package:rideoon/models/auth/account.dart';
import 'package:rideoon/models/api_response.dart';

/// Service class for handling authentication-related API calls
class AuthService {
  static final String _baseUrl = ConfigService.apiBaseUrl;
  static final int _timeout = ConfigService.apiTimeout;

  /// Sign up a new user
  /// 
  /// Takes a [SignUpRequest] and returns an [ApiResponse<SignUpResponse>]
  /// The API endpoint is /v1/client/sign-up
  static Future<ApiResponse<SignUpResponse>> signUp(SignUpRequest request) async {
    try {
      final uri = Uri.parse('$_baseUrl/v1/client/sign-up');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
        'x-request-referral': 'mobile-app', // Required header for endpoint connections
      };

      // Add API key if available
      final apiKey = ConfigService.apiKey;
      if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
        headers['X-API-Key'] = apiKey;
        headers['Authorization'] = 'Bearer $apiKey';
      }

      if (ConfigService.enableDebugMode) {
        print('AuthService: Making sign-up request to $uri');
        print('AuthService: API Key: "$apiKey"');
        print('AuthService: Headers: $headers');
        print('AuthService: Request body: ${jsonEncode(request.toJson())}');
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(request.toJson()),
      ).timeout(Duration(milliseconds: _timeout));

      if (ConfigService.enableDebugMode) {
        print('AuthService: Response status: ${response.statusCode}');
        print('AuthService: Response body: ${response.body}');
      }

      // Check if response is HTML (common when API is behind a proxy/firewall)
      if (response.body.trim().startsWith('<!DOCTYPE html>') ||
          response.body.trim().startsWith('<html')) {

        // Extract meaningful message from HTML if possible
        String errorMessage = 'Server returned HTML instead of JSON';
        if (response.body.contains('missed the magic word')) {
          errorMessage = 'API authentication failed - missing required parameters';
        } else if (response.body.contains('Powered by Greybox')) {
          errorMessage = 'API endpoint may be behind a proxy or firewall';
        }

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }

      // Parse response body
      Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return ApiResponse.error(
          message: 'Invalid JSON response from server: ${e.toString()}',
          statusCode: response.statusCode,
        );
      }

      // Handle successful response (2xx status codes)
      if (response.statusCode >= 200 && response.statusCode < 300) {
        // Handle the API's response format which includes status field
        String successMessage = 'Sign up successful!';

        // Check if response has a message field
        if (responseData.containsKey('message')) {
          successMessage = responseData['message'] as String;
        } else if (response.statusCode == 201) {
          successMessage = 'Account created successfully!';
        }

        // Create a SignUpResponse from the API data
        final signUpResponse = SignUpResponse(
          success: true,
          message: successMessage,
          userId: responseData['id'] as String?,
          token: responseData['token'] as String?,
          userData: responseData,
        );

        return ApiResponse.success(
          message: successMessage,
          data: signUpResponse,
          statusCode: response.statusCode,
        );
      }
      // Handle client errors (4xx status codes)
      else if (response.statusCode >= 400 && response.statusCode < 500) {
        final errorMessage = responseData['message'] as String? ?? 
                           responseData['error'] as String? ?? 
                           'Sign up failed. Please check your information.';
        
        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
          errors: responseData['errors'] as Map<String, dynamic>?,
        );
      }
      // Handle server errors (5xx status codes)
      else {
        return ApiResponse.error(
          message: 'Server error. Please try again later.',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      return ApiResponse.error(
        message: 'No internet connection. Please check your network.',
      );
    } on HttpException catch (e) {
      return ApiResponse.error(
        message: 'Network error: ${e.message}',
      );
    } on FormatException {
      return ApiResponse.error(
        message: 'Invalid response format from server',
      );
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Unexpected error: $e');
      }
      return ApiResponse.error(
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Sign in a user (client or delivery partner)
  ///
  /// Takes a [SignInRequest] and returns an [ApiResponse<SignInResponse>]
  /// The API endpoint is /v1/user/sign-in
  static Future<ApiResponse<SignInResponse>> signIn(SignInRequest request) async {
    try {
      final uri = Uri.parse('$_baseUrl/v1/user/sign-in');
      final headers = {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
        'x-request-referral': 'mobile-app', // Required header for endpoint connections
      };

      // Add API key if available
      final apiKey = ConfigService.apiKey;
      if (apiKey.isNotEmpty && apiKey != 'dev_api_key_placeholder') {
        headers['X-API-Key'] = apiKey;
        headers['Authorization'] = 'Bearer $apiKey';
      }

      if (ConfigService.enableDebugMode) {
        print('AuthService: Making sign-in request to $uri');
        print('AuthService: Request body: ${jsonEncode(request.toJson())}');
      }

      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(request.toJson()),
      ).timeout(Duration(milliseconds: _timeout));

      if (ConfigService.enableDebugMode) {
        print('AuthService: Response status: ${response.statusCode}');
        print('AuthService: Response body: ${response.body}');
      }

      // Check if response is HTML (common when API is behind a proxy/firewall)
      if (response.body.trim().startsWith('<!DOCTYPE html>') ||
          response.body.trim().startsWith('<html')) {

        // Extract meaningful message from HTML if possible
        String errorMessage = 'Server returned HTML instead of JSON';
        if (response.body.contains('missed the magic word')) {
          errorMessage = 'API authentication failed - missing required parameters';
        } else if (response.body.contains('Powered by Greybox')) {
          errorMessage = 'API endpoint may be behind a proxy or firewall';
        }

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
        );
      }

      // Parse response body
      Map<String, dynamic> responseData;
      try {
        responseData = jsonDecode(response.body) as Map<String, dynamic>;
      } catch (e) {
        return ApiResponse.error(
          message: 'Invalid JSON response from server: ${e.toString()}',
          statusCode: response.statusCode,
        );
      }

      // Handle successful response (2xx status codes)
      if (response.statusCode >= 200 && response.statusCode < 300) {
        final signInResponse = SignInResponse.fromJson(responseData);

        // Store authentication data
        await _storeAuthData(signInResponse.token, signInResponse.account);

        return ApiResponse.success(
          message: 'Sign in successful!',
          data: signInResponse,
          statusCode: response.statusCode,
        );
      }
      // Handle client errors (4xx status codes)
      else if (response.statusCode >= 400 && response.statusCode < 500) {
        final errorMessage = responseData['message'] as String? ??
                           responseData['error'] as String? ??
                           'Sign in failed. Please check your credentials.';

        return ApiResponse.error(
          message: errorMessage,
          statusCode: response.statusCode,
          errors: responseData['errors'] as Map<String, dynamic>?,
        );
      }
      // Handle server errors (5xx status codes)
      else {
        return ApiResponse.error(
          message: 'Server error. Please try again later.',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      return ApiResponse.error(
        message: 'No internet connection. Please check your network.',
      );
    } on HttpException catch (e) {
      return ApiResponse.error(
        message: 'Network error: ${e.message}',
      );
    } on FormatException {
      return ApiResponse.error(
        message: 'Invalid response format from server',
      );
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Unexpected error: $e');
      }
      return ApiResponse.error(
        message: 'An unexpected error occurred. Please try again.',
      );
    }
  }

  /// Store authentication data locally
  static Future<void> _storeAuthData(String token, Account account) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('auth_token', token);
      await prefs.setString('user_account', jsonEncode(account.toJson()));
      await prefs.setBool('is_logged_in', true);
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error storing auth data: $e');
      }
    }
  }

  /// Get stored authentication token
  static Future<String?> getAuthToken() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString('auth_token');
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error getting auth token: $e');
      }
      return null;
    }
  }

  /// Get stored user account
  static Future<Account?> getUserAccount() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final accountJson = prefs.getString('user_account');
      if (accountJson != null) {
        final accountData = jsonDecode(accountJson) as Map<String, dynamic>;
        return Account.fromJson(accountData);
      }
      return null;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error getting user account: $e');
      }
      return null;
    }
  }

  /// Check if user is logged in
  static Future<bool> isLoggedIn() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getBool('is_logged_in') ?? false;
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error checking login status: $e');
      }
      return false;
    }
  }

  /// Sign out user (clear stored data)
  static Future<void> signOut() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('auth_token');
      await prefs.remove('user_account');
      await prefs.setBool('is_logged_in', false);
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('AuthService: Error signing out: $e');
      }
    }
  }

  /// Validate email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// Validate phone number format
  static bool isValidPhoneNumber(String phoneNumber) {
    // Remove all non-digit characters for validation
    final digitsOnly = phoneNumber.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Check for various valid formats:
    // Nigerian format: 0XXXXXXXXX (11 digits starting with 0)
    // International format: +234XXXXXXXXX (13 digits starting with +234)
    // International without +: 234XXXXXXXXX (12 digits starting with 234)
    // General international: +XXXXXXXXXXX (7-15 digits with +)

    // Nigerian local format (0XXXXXXXXX)
    if (RegExp(r'^0[789][01]\d{8}$').hasMatch(digitsOnly)) {
      return true;
    }

    // Nigerian international format (+234XXXXXXXXX)
    if (RegExp(r'^\+234[789][01]\d{8}$').hasMatch(digitsOnly)) {
      return true;
    }

    // Nigerian international without + (234XXXXXXXXX)
    if (RegExp(r'^234[789][01]\d{8}$').hasMatch(digitsOnly)) {
      return true;
    }

    // General international format (+XXXXXXXXXXX)
    if (RegExp(r'^\+[1-9]\d{6,14}$').hasMatch(digitsOnly)) {
      return true;
    }

    // General format for other countries (7-15 digits)
    if (RegExp(r'^[1-9]\d{6,14}$').hasMatch(digitsOnly)) {
      return true;
    }

    return false;
  }

  /// Validate password strength
  static bool isValidPassword(String password) {
    return password.length >= 6;
  }

  /// Check if authentication service is properly configured
  static bool get isConfigured {
    return ConfigService.hasValue('API_BASE_URL');
  }

  /// Get authentication service configuration info (for debugging)
  static Map<String, dynamic> getConfigInfo() {
    if (!ConfigService.isDevelopment) {
      throw Exception('Config info can only be accessed in development mode');
    }
    
    return {
      'baseUrl': _baseUrl,
      'timeout': _timeout,
      'environment': ConfigService.appEnvironment,
      'isConfigured': isConfigured,
    };
  }
}
