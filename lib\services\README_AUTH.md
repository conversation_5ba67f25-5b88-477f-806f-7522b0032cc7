# Authentication Service Documentation

## Overview

This document describes the authentication service implementation for the RideOn app, including the API integration for user sign-up functionality.

## API Configuration

The authentication service uses the following API endpoints:
- **Base URL**: `https://riiideon-app.azurewebsites.net/api`
- **Sign-up Endpoint**: `/v1/client/sign-up`
- **Sign-in Endpoint**: `/v1/user/sign-in`
- **Method**: POST
- **Content-Type**: application/json

## Models

### SignUpRequest (`lib/models/auth/sign_up_request.dart`)

Represents the data required for user registration:

```dart
class SignUpRequest {
  final String email;
  final String password;
  final String repeatedPassword;
  final String firstName;
  final String lastName;
  final String phoneNumber;
}
```

**JSON Format:**
```json
{
  "email": "<EMAIL>",
  "password": "yourPassword123",
  "repeatedPassword": "yourPassword123",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "phoneNumber": "7012345678"
}
```

### SignInRequest (`lib/models/auth/sign_in_request.dart`)

Represents the data required for user authentication:

```dart
class SignInRequest {
  final String email;
  final String password;
}
```

**JSON Format:**
```json
{
  "email": "<EMAIL>",
  "password": "yourPassword123"
}
```

### SignUpResponse (`lib/models/auth/sign_up_response.dart`)

Represents the API response for user registration:

```dart
class SignUpResponse {
  final bool success;
  final String message;
  final String? userId;
  final String? token;
  final Map<String, dynamic>? userData;
}
```

### SignInResponse (`lib/models/auth/sign_in_response.dart`)

Represents the API response for user authentication:

```dart
class SignInResponse {
  final int status;
  final String token;
  final Account account;
}
```

**JSON Format:**
```json
{
  "status": 200,
  "token": "64nc576t7r98ct7n6578wn90cmu8r99id97ty7nc7w09",
  "account": {
    "uuid": "df0921a1-261a-40ba-915c-8465d258892d",
    "firstName": "Emma",
    "lastName": "Watson",
    "email": "<EMAIL>",
    "type": "client"
  }
}
```

### Account (`lib/models/auth/account.dart`)

Represents user account information:

```dart
class Account {
  final String uuid;
  final String firstName;
  final String lastName;
  final String email;
  final String type; // "client" or "delivery"
  final bool verified;
  // ... other fields
}
```

### ApiResponse (`lib/models/api_response.dart`)

Generic wrapper for API responses with error handling:

```dart
class ApiResponse<T> {
  final bool success;
  final String message;
  final T? data;
  final int? statusCode;
  final Map<String, dynamic>? errors;
}
```

## Service

### AuthService (`lib/services/auth_service.dart`)

Main service class for authentication operations:

#### Methods

**Authentication:**
- `signUp(SignUpRequest request)` - Registers a new user
- `signIn(SignInRequest request)` - Authenticates a user (client or delivery partner)

**Token Management:**
- `getAuthToken()` - Retrieves stored authentication token
- `getUserAccount()` - Retrieves stored user account information
- `isLoggedIn()` - Checks if user is currently logged in
- `signOut()` - Clears stored authentication data

**Remember Me Functionality:**
- `saveRememberMeCredentials(email, password)` - Saves user credentials for auto-login
- `getRememberMeCredentials()` - Retrieves saved credentials if enabled
- `isRememberMeEnabled()` - Checks if remember me is enabled
- `clearRememberMeCredentials()` - Clears saved credentials

**Validation:**
- `isValidEmail(String email)` - Validates email format
- `isValidPhoneNumber(String phoneNumber)` - Validates phone number format
- `isValidPassword(String password)` - Validates password strength

#### Usage Example

```dart
final request = SignUpRequest(
  email: '<EMAIL>',
  password: 'password123',
  repeatedPassword: 'password123',
  firstName: 'John',
  lastName: 'Doe',
  phoneNumber: '**********',
);

final response = await AuthService.signUp(request);

if (response.success) {
  // Handle successful registration
  print('User registered: ${response.message}');
} else {
  // Handle error
  print('Registration failed: ${response.message}');
}
```

#### Sign-In Usage Example

```dart
final request = SignInRequest(
  email: '<EMAIL>',
  password: 'password123',
);

final response = await AuthService.signIn(request);

if (response.success && response.data != null) {
  final account = response.data!.account;
  print('Welcome back, ${account.firstName}!');

  // Navigate based on user type
  if (account.isClient) {
    // Navigate to user dashboard
  } else if (account.isRider) {
    // Navigate to rider dashboard
  }
} else {
  // Handle error
  print('Sign in failed: ${response.message}');
}
```

#### Remember Me Usage Example

```dart
// Check if remember me credentials exist on app start
final credentials = await AuthService.getRememberMeCredentials();
if (credentials != null) {
  // Auto-fill login form
  emailController.text = credentials['email']!;
  passwordController.text = credentials['password']!;
  rememberMeChecked = true;
}

// Save credentials on successful login (if remember me is checked)
if (rememberMeChecked && loginSuccessful) {
  await AuthService.saveRememberMeCredentials(email, password);
}

// Clear credentials when remember me is unchecked
if (!rememberMeChecked) {
  await AuthService.clearRememberMeCredentials();
}
```

## UI Integration

### UserSignUp Widget (`lib/views/authentication/userauth/sign_up.dart`)

The sign-up form includes the following fields:
- First Name (required, min 2 characters)
- Last Name (required, min 2 characters)
- Email (required, valid email format)
- Phone Number (required, valid phone format)
- Password (required, min 6 characters)
- Confirm Password (required, must match password)
- Terms and Conditions checkbox (required)

#### Form Validation

All fields are validated both on the client side and through the AuthService validation methods:

- **Email**: Uses `AuthService.isValidEmail()`
- **Phone**: Uses `AuthService.isValidPhoneNumber()`
- **Password**: Uses `AuthService.isValidPassword()`
- **Names**: Minimum 2 characters each
- **Password Confirmation**: Must match the password field

#### Error Handling

The form handles various error scenarios:
- Network connectivity issues
- Server errors (4xx, 5xx status codes)
- Invalid response formats
- Validation errors from the API

## Testing

### Unit Tests (`test/auth_service_test.dart`)

Comprehensive tests for:
- Model serialization/deserialization
- Validation methods
- API response handling
- Error scenarios

### Widget Tests (`test/user_signup_test.dart`)

UI tests for:
- Responsive design across different screen sizes
- Form validation
- User interactions
- Accessibility

## Configuration

### Environment Variables

Required environment variables in `.env`:

```env
API_BASE_URL=https://riiideon-app.azurewebsites.net/api
API_TIMEOUT=30000
ENABLE_DEBUG_MODE=true
```

### Dependencies

Required packages in `pubspec.yaml`:

```yaml
dependencies:
  http: ^1.4.0
  flutter_dotenv: ^5.2.1
```

## Error Handling

The service implements comprehensive error handling for:

1. **Network Errors**: No internet connection, timeouts
2. **HTTP Errors**: 4xx client errors, 5xx server errors
3. **Parsing Errors**: Invalid JSON responses
4. **Validation Errors**: Client-side validation failures

All errors are wrapped in `ApiResponse` objects with appropriate error messages for user display.

## Security Considerations

- Passwords are transmitted securely over HTTPS
- No sensitive data is logged in production
- Input validation prevents common injection attacks
- API responses are properly sanitized before display
