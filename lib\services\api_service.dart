import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:rideoon/services/config_service.dart';

/// Example API service that demonstrates how to use ConfigService
/// This shows how to access environment variables in your API calls
class ApiService {
  static final String _baseUrl = ConfigService.apiBaseUrl;
  static final String _apiKey = ConfigService.apiKey;
  static final int _timeout = ConfigService.apiTimeout;

  /// Example method to make an authenticated API call
  static Future<Map<String, dynamic>?> makeAuthenticatedRequest(
    String endpoint, {
    Map<String, dynamic>? body,
    String method = 'GET',
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final headers = {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_apiKey',
        'User-Agent': '${ConfigService.appName}/${ConfigService.appVersion}',
      };

      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: headers)
              .timeout(Duration(milliseconds: _timeout));
          break;
        case 'POST':
          response = await http.post(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(Duration(milliseconds: _timeout));
          break;
        case 'PUT':
          response = await http.put(
            uri,
            headers: headers,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(Duration(milliseconds: _timeout));
          break;
        case 'DELETE':
          response = await http.delete(uri, headers: headers)
              .timeout(Duration(milliseconds: _timeout));
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        throw HttpException(
          'API request failed with status ${response.statusCode}: ${response.body}',
        );
      }
    } catch (e) {
      if (ConfigService.enableDebugMode) {
        print('API Error: $e');
      }
      rethrow;
    }
  }

  /// Example method to get user profile
  static Future<Map<String, dynamic>?> getUserProfile(String userId) async {
    return await makeAuthenticatedRequest('/users/$userId');
  }

  /// Example method to update user profile
  static Future<Map<String, dynamic>?> updateUserProfile(
    String userId,
    Map<String, dynamic> profileData,
  ) async {
    return await makeAuthenticatedRequest(
      '/users/$userId',
      body: profileData,
      method: 'PUT',
    );
  }

  /// Check if API is configured properly
  static bool get isConfigured {
    return ConfigService.hasValue('API_BASE_URL') && 
           ConfigService.hasValue('API_KEY');
  }

  /// Get API configuration info (for debugging)
  static Map<String, dynamic> getConfigInfo() {
    if (!ConfigService.isDevelopment) {
      throw Exception('Config info can only be accessed in development mode');
    }
    
    return {
      'baseUrl': _baseUrl,
      'timeout': _timeout,
      'hasApiKey': ConfigService.hasValue('API_KEY'),
      'environment': ConfigService.appEnvironment,
    };
  }
}
