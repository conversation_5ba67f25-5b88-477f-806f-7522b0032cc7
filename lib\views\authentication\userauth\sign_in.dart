import 'package:flutter/material.dart';
import 'package:rideoon/views/custom_widgets/app_colors.dart';
import 'package:rideoon/views/authentication/userauth/sign_up.dart';
import 'package:rideoon/views/authentication/userauth/password_recovery_and_reset/forgot_password.dart';
import 'package:rideoon/views/dashboards/user_dashboard/dashboard_screens.dart';
import 'package:rideoon/views/dashboards/rider_dashboard/dashboard_screens.dart';
import 'package:rideoon/services/auth_service.dart';
import 'package:rideoon/models/auth/sign_in_request.dart';
import 'package:rideoon/providers/toast_provider.dart';

class UserSignIn extends StatefulWidget {
  const UserSignIn({super.key});

  @override
  State<UserSignIn> createState() => _UserSignInState();
}

class _UserSignInState extends State<UserSignIn> {
  final _formKey = GlobalKey<FormState>();
  final _emailPhoneController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isPasswordVisible = false;
  bool _isLoading = false;
  bool _rememberMe = false;

  @override
  void initState() {
    super.initState();
    _loadRememberMeCredentials();
  }

  @override
  void dispose() {
    _emailPhoneController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  /// Load saved credentials if "Remember me" was enabled
  Future<void> _loadRememberMeCredentials() async {
    try {
      final isEnabled = await AuthService.isRememberMeEnabled();
      if (isEnabled) {
        final credentials = await AuthService.getRememberMeCredentials();
        if (credentials != null) {
          setState(() {
            _emailPhoneController.text = credentials['email'] ?? '';
            _passwordController.text = credentials['password'] ?? '';
            _rememberMe = true;
          });
        }
      }
    } catch (e) {
      // Silently handle errors - don't show error to user for this
      if (mounted) {
        // Could log this for debugging if needed
      }
    }
  }

  void _togglePasswordVisibility() {
    setState(() {
      _isPasswordVisible = !_isPasswordVisible;
    });
  }

  Future<void> _toggleRememberMe() async {
    setState(() {
      _rememberMe = !_rememberMe;
    });

    // If unchecked, clear saved credentials
    if (!_rememberMe) {
      try {
        await AuthService.clearRememberMeCredentials();
      } catch (e) {
        // Silently handle errors
      }
    }
  }

  void _handleForgotPassword() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => const UserForgotPassword(),
      ),
    );
  }

  void _navigateToSignUp() {
    Navigator.of(context).pushReplacement(
      MaterialPageRoute(
        builder: (context) => const UserSignUp(),
      ),
    );
  }

  Future<void> _handleSignIn() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Create sign-in request
      final signInRequest = SignInRequest(
        email: _emailPhoneController.text.trim(),
        password: _passwordController.text,
      );

      // Call the authentication service
      final response = await AuthService.signIn(signInRequest);

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (response.success && response.data != null) {
          final account = response.data!.account;

          // Save credentials if "Remember me" is checked
          if (_rememberMe) {
            try {
              await AuthService.saveRememberMeCredentials(
                _emailPhoneController.text.trim(),
                _passwordController.text,
              );
            } catch (e) {
              // Silently handle errors - don't interrupt successful login
            }
          }

          // Show success message
          Toast.success('Welcome back, ${account.firstName}!');

          // Navigate to appropriate dashboard based on user type
          if (mounted) {
            if (account.isClient) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const UserDashboardScreens(),
                ),
              );
            } else if (account.isRider) {
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const RiderDashboardScreens(),
                ),
              );
            } else {
              // Default to user dashboard if type is unclear
              Navigator.of(context).pushReplacement(
                MaterialPageRoute(
                  builder: (context) => const UserDashboardScreens(),
                ),
              );
            }
          }
        } else {
          // Show error message
          Toast.error(response.message);
        }
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        Toast.error('An unexpected error occurred. Please try again.');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Container(
            constraints: BoxConstraints(
              minHeight: MediaQuery.of(context).size.height - MediaQuery.of(context).padding.top,
            ),
            child: Padding(
              padding: EdgeInsets.symmetric(
                horizontal: _getHorizontalPadding(context),
                vertical: _getVerticalPadding(context),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: _getSpacing(context, 40)),

                  // Welcome text
                  _buildWelcomeText(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Sign in form
                  _buildSignInForm(context),

                  SizedBox(height: _getSpacing(context, 32)),

                  // Sign in button
                  _buildSignInButton(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Or divider
                  _buildOrDivider(context),

                  SizedBox(height: _getSpacing(context, 24)),

                  // Sign up link
                  _buildSignUpLink(context),

                  SizedBox(height: _getSpacing(context, 20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildWelcomeText(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Welcome Back',
          style: TextStyle(
            fontSize: _getHeadingFontSize(context),
            fontWeight: FontWeight.w700,
            color: AppColors.black,
          ),
        ),
        SizedBox(height: _getSpacing(context, 8)),
        Text(
          'Sign in to your account to continue',
          style: TextStyle(
            fontSize: _getBodyFontSize(context),
            fontWeight: FontWeight.w400,
            color: AppColors.black.withValues(alpha: 0.6),
          ),
        ),
      ],
    );
  }

  Widget _buildSignInForm(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          // Email/Phone Field
          _buildTextFormField(
            controller: _emailPhoneController,
            labelText: 'Email/Phone',
            hintText: 'Enter your email/phone number',
            prefixIcon: Icons.alternate_email,
            keyboardType: TextInputType.emailAddress,
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your email or phone number';
              }
              // Check if it's a valid email or phone number
              bool isEmail = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value);
              bool isPhone = RegExp(r'^[\+]?[1-9][\d]{0,15}$').hasMatch(value.replaceAll(RegExp(r'[\s\-\(\)]'), ''));

              if (!isEmail && !isPhone) {
                return 'Please enter a valid email or phone number';
              }
              return null;
            },
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Password Field
          _buildTextFormField(
            controller: _passwordController,
            labelText: 'Password',
            hintText: 'Enter your password',
            prefixIcon: Icons.lock_outline,
            obscureText: !_isPasswordVisible,
            suffixIcon: IconButton(
              onPressed: _togglePasswordVisibility,
              icon: Icon(
                _isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                color: AppColors.black.withValues(alpha: 0.6),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter your password';
              }
              return null;
            },
          ),

          SizedBox(height: _getSpacing(context, 16)),

          // Remember me and Forgot password
          _buildRememberMeAndForgotPassword(context),
        ],
      ),
    );
  }

  Widget _buildTextFormField({
    required TextEditingController controller,
    required String labelText,
    required String hintText,
    required IconData prefixIcon,
    TextInputType? keyboardType,
    bool obscureText = false,
    Widget? suffixIcon,
    String? Function(String?)? validator,
  }) {
    return TextFormField(
      controller: controller,
      keyboardType: keyboardType,
      obscureText: obscureText,
      validator: validator,
      style: TextStyle(
        fontSize: _getBodyFontSize(context),
        color: AppColors.black,
      ),
      decoration: InputDecoration(
        labelText: labelText,
        hintText: hintText,
        prefixIcon: Icon(
          prefixIcon,
          color: AppColors.black.withValues(alpha: 0.6),
          size: _getIconSize(context),
        ),
        suffixIcon: suffixIcon,
        labelStyle: TextStyle(
          fontSize: _getBodyFontSize(context),
          color: AppColors.black.withValues(alpha: 0.6),
        ),
        hintStyle: TextStyle(
          fontSize: _getBodyFontSize(context),
          color: AppColors.black.withValues(alpha: 0.4),
        ),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: BorderSide(
            color: AppColors.black.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: AppColors.primary,
            width: 2,
          ),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 1,
          ),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          borderSide: const BorderSide(
            color: Colors.red,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: _getSpacing(context, 16),
          vertical: _getSpacing(context, 16),
        ),
      ),
    );
  }

  Widget _buildRememberMeAndForgotPassword(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Remember me checkbox
        Expanded(
          child: Row(
            children: [
              SizedBox(
                width: _getCheckboxSize(context),
                height: _getCheckboxSize(context),
                child: Checkbox(
                  value: _rememberMe,
                  onChanged: (value) => _toggleRememberMe(),
                  activeColor: AppColors.primary,
                  checkColor: AppColors.white,
                  side: BorderSide(
                    color: AppColors.black.withValues(alpha: 0.3),
                    width: 1.5,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(_getSpacing(context, 3)),
                  ),
                ),
              ),
              SizedBox(width: _getSpacing(context, 8)),
              Expanded(
                child: GestureDetector(
                  onTap: _toggleRememberMe,
                  child: Text(
                    'Remember me',
                    style: TextStyle(
                      fontSize: _getSmallFontSize(context),
                      color: AppColors.black.withValues(alpha: 0.7),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),

        // Forgot password
        GestureDetector(
          onTap: _handleForgotPassword,
          child: Text(
            'Forgot password?',
            style: TextStyle(
              fontSize: _getSmallFontSize(context),
              color: AppColors.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignInButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      height: _getButtonHeight(context),
      child: ElevatedButton(
        onPressed: _isLoading ? null : _handleSignIn,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_getBorderRadius(context)),
          ),
          elevation: 0,
        ),
        child: _isLoading
            ? SizedBox(
                width: _getSpacing(context, 20),
                height: _getSpacing(context, 20),
                child: const CircularProgressIndicator(
                  color: AppColors.white,
                  strokeWidth: 2,
                ),
              )
            : Text(
                'Sign in',
                style: TextStyle(
                  fontSize: _getBodyFontSize(context),
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  Widget _buildOrDivider(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.black.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
          ),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: _getSpacing(context, 16)),
          child: Text(
            'Or',
            style: TextStyle(
              fontSize: _getSmallFontSize(context),
              color: AppColors.black.withValues(alpha: 0.6),
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        Expanded(
          child: Container(
            height: 1,
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: AppColors.black.withValues(alpha: 0.2),
                  width: 1,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSignUpLink(BuildContext context) {
    return Center(
      child: GestureDetector(
        onTap: _navigateToSignUp,
        child: RichText(
          text: TextSpan(
            style: TextStyle(
              fontSize: _getSmallFontSize(context),
              fontFamily: 'Inter',
            ),
            children: [
              TextSpan(
                text: 'Don\'t have an account? ',
                style: TextStyle(
                  color: AppColors.black.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w400,
                ),
              ),
              const TextSpan(
                text: 'Sign up',
                style: TextStyle(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // Responsive helper methods
  double _getHorizontalPadding(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double basePadding;
    if (screenWidth < 300) {
      basePadding = 16; // Smartwatch
    } else if (screenWidth > 600) {
      basePadding = 40; // Tablet
    } else {
      basePadding = 24; // Mobile
    }

    if (isShortScreen) {
      basePadding = basePadding * 0.8;
    }

    return basePadding;
  }

  double _getVerticalPadding(BuildContext context) {
    final screenHeight = MediaQuery.of(context).size.height;
    if (screenHeight < 600) return 12; // Short screens
    if (screenHeight > 800) return 24; // Tall screens
    return 16; // Normal screens
  }

  double _getSpacing(BuildContext context, double baseSpacing) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double spacing;
    if (screenWidth < 300) {
      spacing = baseSpacing * 0.6; // Smartwatch
    } else if (screenWidth > 600) {
      spacing = baseSpacing * 1.2; // Tablet
    } else {
      spacing = baseSpacing; // Mobile
    }

    if (isShortScreen) {
      spacing = spacing * 0.8;
    }

    return spacing;
  }

  double _getHeadingFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 20; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 32; // Tablet
    } else {
      baseSize = 26; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.85;
    }

    return baseSize;
  }

  double _getBodyFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 12; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 16; // Tablet
    } else {
      baseSize = 14; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getSmallFontSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 10; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 14; // Tablet
    } else {
      baseSize = 12; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }

  double _getIconSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 16; // Smartwatch
    if (screenWidth > 600) return 24; // Tablet
    return 20; // Mobile
  }

  double _getBorderRadius(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth < 300) return 6; // Smartwatch
    if (screenWidth > 600) return 12; // Tablet
    return 8; // Mobile
  }

  double _getButtonHeight(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseHeight;
    if (screenWidth < 300) {
      baseHeight = 40; // Smartwatch
    } else if (screenWidth > 600) {
      baseHeight = 56; // Tablet
    } else {
      baseHeight = 48; // Mobile
    }

    if (isShortScreen) {
      baseHeight = baseHeight * 0.9;
    }

    return baseHeight;
  }

  double _getCheckboxSize(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isShortScreen = screenHeight < 600;

    double baseSize;
    if (screenWidth < 300) {
      baseSize = 16; // Smartwatch
    } else if (screenWidth > 600) {
      baseSize = 22; // Tablet
    } else {
      baseSize = 18; // Mobile
    }

    if (isShortScreen) {
      baseSize = baseSize * 0.9;
    }

    return baseSize;
  }
}